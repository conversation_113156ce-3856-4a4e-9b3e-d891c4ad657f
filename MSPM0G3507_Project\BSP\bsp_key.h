#ifndef __BSP_KEY_H__
#define __BSP_KEY_H__

#include "ti_msp_dl_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 按键状态定义 */
#define KEY_PRESSED     1
#define KEY_RELEASED    0

/* 按键引脚定义 */
#define KEY_PORT        KEY_PORT
#define KEY_PIN         KEY_PIN_18_PIN

/* 按键控制函数声明 */
void BSP_KEY_Init(void);
uint8_t BSP_KEY_Read(void);
uint8_t BSP_KEY_Scan(void);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_KEY_H__ */
