/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3505" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.21.1+3772"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

GPIO1.$name                              = "LED1";
GPIO1.port                               = "PORTB";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].$name            = "PIN_2";
GPIO1.associatedPins[0].assignedPin      = "2";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "KEY";
GPIO2.port                               = "PORTA";
GPIO2.associatedPins[0].$name            = "PIN_18";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].assignedPin      = "18";
GPIO2.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[0].interruptEn      = true;
GPIO2.associatedPins[0].polarity         = "RISE";
GPIO2.associatedPins[0].pin.$assign      = "PA18";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable      = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";
SYSTICK.period            = 32;

UART1.$name                    = "UART_0";
UART1.uartClkSrc               = "MFCLK";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution = "PB2";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
