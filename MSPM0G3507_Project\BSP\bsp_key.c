#include "bsp_key.h"

/**
 * @brief 按键初始化
 */
void BSP_KEY_Init(void)
{
    /* 按键引脚已在ti_msp_dl_config.c中初始化 */
    /* 配置为输入模式，内部下拉 */
}

/**
 * @brief 读取按键状态
 * @return 按键状态 (KEY_PRESSED/KEY_RELEASED)
 */
uint8_t BSP_KEY_Read(void)
{
    if (DL_GPIO_readPins(KEY_PORT, KEY_PIN_18_PIN)) {
        return KEY_PRESSED;
    } else {
        return KEY_RELEASED;
    }
}

/**
 * @brief 按键扫描（带消抖）
 * @return 按键状态 (KEY_PRESSED/KEY_RELEASED)
 */
uint8_t BSP_KEY_Scan(void)
{
    static uint8_t key_state = KEY_RELEASED;
    static uint32_t key_count = 0;
    
    if (BSP_KEY_Read() == KEY_PRESSED) {
        key_count++;
        if (key_count >= 3) {  /* 消抖计数 */
            if (key_state == KEY_RELEASED) {
                key_state = KEY_PRESSED;
                return KEY_PRESSED;
            }
        }
    } else {
        key_count = 0;
        key_state = KEY_RELEASED;
    }
    
    return KEY_RELEASED;
}
