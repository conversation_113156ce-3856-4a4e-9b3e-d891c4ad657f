#include "bsp_led.h"

/**
 * @brief LED初始化
 */
void BSP_LED_Init(void)
{
    /* LED引脚已在ti_msp_dl_config.c中初始化 */
    /* 默认关闭LED */
    BSP_LED_Off();
}

/**
 * @brief 点亮LED
 */
void BSP_LED_On(void)
{
    DL_GPIO_setPins(LED1_PORT, LED1_PIN_2_PIN);
}

/**
 * @brief 熄灭LED
 */
void BSP_LED_Off(void)
{
    DL_GPIO_clearPins(LED1_PORT, LED1_PIN_2_PIN);
}

/**
 * @brief LED状态翻转
 */
void BSP_LED_Toggle(void)
{
    DL_GPIO_togglePins(LED1_PORT, LED1_PIN_2_PIN);
}

/**
 * @brief 设置LED状态
 * @param state LED状态 (LED_ON/LED_OFF)
 */
void BSP_LED_Set(uint8_t state)
{
    if (state == LED_ON) {
        BSP_LED_On();
    } else {
        BSP_LED_Off();
    }
}
