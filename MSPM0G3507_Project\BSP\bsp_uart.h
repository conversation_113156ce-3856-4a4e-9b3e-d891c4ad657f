#ifndef __BSP_UART_H__
#define __BSP_UART_H__

#include "ti_msp_dl_config.h"
#include <stdio.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

/* UART缓冲区大小 */
#define UART_RX_BUFFER_SIZE     256
#define UART_TX_BUFFER_SIZE     256

/* UART实例定义 */
#define BSP_UART_INST           UART_0_INST

/* UART控制函数声明 */
void BSP_UART_Init(void);
void BSP_UART_SendChar(char ch);
void BSP_UART_SendString(const char* str);
void BSP_UART_SendData(const uint8_t* data, uint16_t len);
uint8_t BSP_UART_ReceiveChar(void);
uint16_t BSP_UART_ReceiveData(uint8_t* buffer, uint16_t max_len);
void BSP_UART_Printf(const char* format, ...);

/* 中断处理函数 */
void BSP_UART_IRQHandler(void);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_UART_H__ */
