#include "bsp_uart.h"
#include <stdarg.h>

/* UART接收缓冲区 */
static uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE];
static volatile uint16_t uart_rx_head = 0;
static volatile uint16_t uart_rx_tail = 0;

/**
 * @brief UART初始化
 */
void BSP_UART_Init(void)
{
    /* UART已在ti_msp_dl_config.c中初始化 */
    /* 清空接收缓冲区 */
    uart_rx_head = 0;
    uart_rx_tail = 0;
}

/**
 * @brief 发送单个字符
 * @param ch 要发送的字符
 */
void BSP_UART_SendChar(char ch)
{
    DL_UART_Main_transmitDataBlocking(BSP_UART_INST, ch);
}

/**
 * @brief 发送字符串
 * @param str 要发送的字符串
 */
void BSP_UART_SendString(const char* str)
{
    while (*str) {
        BSP_UART_SendChar(*str++);
    }
}

/**
 * @brief 发送数据
 * @param data 要发送的数据
 * @param len 数据长度
 */
void BSP_UART_SendData(const uint8_t* data, uint16_t len)
{
    for (uint16_t i = 0; i < len; i++) {
        BSP_UART_SendChar(data[i]);
    }
}

/**
 * @brief 接收单个字符
 * @return 接收到的字符，如果没有数据返回0
 */
uint8_t BSP_UART_ReceiveChar(void)
{
    if (uart_rx_head != uart_rx_tail) {
        uint8_t ch = uart_rx_buffer[uart_rx_tail];
        uart_rx_tail = (uart_rx_tail + 1) % UART_RX_BUFFER_SIZE;
        return ch;
    }
    return 0;
}

/**
 * @brief 接收数据
 * @param buffer 接收缓冲区
 * @param max_len 最大接收长度
 * @return 实际接收到的数据长度
 */
uint16_t BSP_UART_ReceiveData(uint8_t* buffer, uint16_t max_len)
{
    uint16_t count = 0;
    while ((count < max_len) && (uart_rx_head != uart_rx_tail)) {
        buffer[count++] = BSP_UART_ReceiveChar();
    }
    return count;
}

/**
 * @brief UART中断处理函数
 */
void BSP_UART_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(BSP_UART_INST)) {
        case DL_UART_MAIN_IIDX_RX:
            /* 接收中断 */
            while (DL_UART_Main_isRXFIFOEmpty(BSP_UART_INST) != true) {
                uint8_t data = DL_UART_Main_receiveDataBlocking(BSP_UART_INST);
                uint16_t next_head = (uart_rx_head + 1) % UART_RX_BUFFER_SIZE;
                if (next_head != uart_rx_tail) {
                    uart_rx_buffer[uart_rx_head] = data;
                    uart_rx_head = next_head;
                }
            }
            break;
        default:
            break;
    }
}
