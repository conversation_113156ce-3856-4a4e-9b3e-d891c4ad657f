#ifndef __BSP_LED_H__
#define __BSP_LED_H__

#include "ti_msp_dl_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* LED控制宏定义 */
#define LED_ON      1
#define LED_OFF     0

/* LED引脚定义 */
#define LED1_PORT   LED1_PORT
#define LED1_PIN    LED1_PIN_2_PIN

/* LED控制函数声明 */
void BSP_LED_Init(void);
void BSP_LED_On(void);
void BSP_LED_Off(void);
void BSP_LED_Toggle(void);
void BSP_LED_Set(uint8_t state);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_LED_H__ */
